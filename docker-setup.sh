#!/bin/bash

# Docker Setup Script for GCP Inventory Tool
# This script helps set up the Docker environment for testing

set -e

echo "🐳 Setting up Docker environment for GCP Inventory Tool"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_warning "Docker Compose not found. Trying docker compose..."
    if ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not available. Please install Docker Compose."
        exit 1
    fi
    DOCKER_COMPOSE="docker compose"
else
    DOCKER_COMPOSE="docker-compose"
fi

print_status "Docker and Docker Compose are available"

# Create necessary directories
print_info "Creating necessary directories..."
mkdir -p credentials output logs docker-config

print_status "Directories created"

# Check if service account key exists
if [ ! -f "credentials/service-account.json" ]; then
    print_warning "Service account key not found at credentials/service-account.json"
    echo ""
    echo "To set up authentication:"
    echo "1. Go to Google Cloud Console"
    echo "2. Navigate to IAM & Admin > Service Accounts"
    echo "3. Create a new service account or use existing one"
    echo "4. Download the JSON key file"
    echo "5. Save it as 'credentials/service-account.json'"
    echo ""
    echo "Required permissions for the service account:"
    echo "- Viewer role on all projects you want to scan"
    echo "- Or specific roles like Compute Viewer, Storage Viewer, etc."
    echo ""
    read -p "Press Enter to continue once you've set up the credentials..."
fi

# Check if config file exists and has projects configured
if [ ! -f "docker-config/config.yaml" ]; then
    print_error "Configuration file not found at docker-config/config.yaml"
    exit 1
fi

# Check if projects are configured
if ! grep -q "your-project-id" docker-config/config.yaml; then
    print_status "Configuration file looks good"
else
    print_warning "Please edit docker-config/config.yaml and add your project IDs"
    echo ""
    echo "Edit the projects section in docker-config/config.yaml:"
    echo "projects:"
    echo "  - \"your-actual-project-id-1\""
    echo "  - \"your-actual-project-id-2\""
    echo ""
    read -p "Press Enter to continue once you've updated the configuration..."
fi

# Build the Docker image
print_info "Building Docker image..."
if docker build -t gcp-inventory-tool .; then
    print_status "Docker image built successfully"
else
    print_error "Failed to build Docker image"
    exit 1
fi

# Test DNS resolution in container
print_info "Testing DNS resolution in container..."
if docker run --rm gcp-inventory-tool python /app/test_dns_resolution.py; then
    print_status "DNS resolution test passed"
else
    print_warning "DNS resolution test had issues, but continuing..."
fi

echo ""
print_status "Setup complete! 🎉"
echo ""
echo "Next steps:"
echo "1. Ensure your service account key is at: credentials/service-account.json"
echo "2. Update project IDs in: docker-config/config.yaml"
echo "3. Run the inventory tool:"
echo ""
echo "   # Using Docker Compose (recommended):"
echo "   $DOCKER_COMPOSE up"
echo ""
echo "   # Or using Docker directly:"
echo "   docker run --rm \\"
echo "     -v \$(pwd)/credentials:/app/credentials:ro \\"
echo "     -v \$(pwd)/docker-config:/app/config:ro \\"
echo "     -v \$(pwd)/output:/app/output \\"
echo "     -v \$(pwd)/logs:/app/logs \\"
echo "     gcp-inventory-tool"
echo ""
echo "4. Check results in: output/ directory"
echo "5. Check logs in: logs/ directory"
echo ""
print_info "For testing DNS fixes specifically, run:"
echo "   $DOCKER_COMPOSE --profile testing up dns-test"
