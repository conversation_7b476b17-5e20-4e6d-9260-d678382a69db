version: '3.8'

services:
  gcp-inventory:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: gcp-inventory-tool
    volumes:
      # Mount your service account key file
      - ./credentials:/app/credentials:ro
      # Mount configuration directory
      - ./docker-config:/app/config:ro
      # Mount output directory to persist results
      - ./output:/app/output
      # Mount logs directory
      - ./logs:/app/logs
    environment:
      # Override environment variables as needed
      - GOOGLE_APPLICATION_CREDENTIALS=/app/credentials/service-account.json
      - PYTHONUNBUFFERED=1
      - LOG_LEVEL=INFO
    # Override the default command if needed
    # command: ["python", "-m", "gcp_inventory_tool", "--config", "/app/config/config.yaml", "--output", "/app/output"]
    
    # Network configuration (if needed)
    # network_mode: "host"
    
    # Resource limits (optional)
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    
    # Restart policy
    restart: unless-stopped

  # Optional: Add a service for testing DNS resolution
  dns-test:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: gcp-dns-test
    volumes:
      - ./credentials:/app/credentials:ro
    command: ["python", "/app/test_dns_resolution.py"]
    profiles:
      - testing
