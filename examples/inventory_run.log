                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          2025-06-09 14:01:00,000 - ERROR - gcp_inventory:cloud_functions.py - [ce-ps3][cloud_function] Failed to list V1 functions: Timeout of 600.0s exceeded, last exception: 503 DNS resolution failed for cloudfunctions.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=cloudfunctions.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/gcp-inventory/Updated-gcp-invnetory/venv/lib/python3.13/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
  File "/Users/<USER>/Downloads/gcp-inventory/Updated-gcp-invnetory/venv/lib/python3.13/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ~~~~~~~~~~~~~~~^
        request,
        ^^^^^^^^
    ...<4 lines>...
        compression=compression,
        ^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/Downloads/gcp-inventory/Updated-gcp-invnetory/venv/lib/python3.13/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ~~~~~~~~~~~^^
  File "/Users/<USER>/Downloads/gcp-inventory/Updated-gcp-invnetory/venv/lib/python3.13/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/gcp-inventory/Updated-gcp-invnetory/venv/lib/python3.13/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        request,
        ^^^^^^^^
    ...<4 lines>...
        compression=new_compression,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/Downloads/gcp-inventory/Updated-gcp-invnetory/venv/lib/python3.13/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
  File "/Users/<USER>/Downloads/gcp-inventory/Updated-gcp-invnetory/venv/lib/python3.13/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for cloudfunctions.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=cloudfunctions.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for cloudfunctions.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=cloudfunctions.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/gcp-inventory/Updated-gcp-invnetory/venv/lib/python3.13/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
  File "/Users/<USER>/Downloads/gcp-inventory/Updated-gcp-invnetory/venv/lib/python3.13/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "/Users/<USER>/Downloads/gcp-inventory/Updated-gcp-invnetory/venv/lib/python3.13/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for cloudfunctions.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=cloudfunctions.googleapis.com is_balancer=0: Timeout while contacting DNS servers

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/gcp-inventory/Updated-gcp-invnetory/gcp_inventory_tool/fetchers/cloud_functions.py", line 240, in fetch_resources
    functions_v1_list = v1_client.list_functions(request=request_v1)
  File "/Users/<USER>/Downloads/gcp-inventory/Updated-gcp-invnetory/venv/lib/python3.13/site-packages/google/cloud/functions_v1/services/cloud_functions_service/client.py", line 855, in list_functions
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "/Users/<USER>/Downloads/gcp-inventory/Updated-gcp-invnetory/venv/lib/python3.13/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "/Users/<USER>/Downloads/gcp-inventory/Updated-gcp-invnetory/venv/lib/python3.13/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "/Users/<USER>/Downloads/gcp-inventory/Updated-gcp-invnetory/venv/lib/python3.13/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "/Users/<USER>/Downloads/gcp-inventory/Updated-gcp-invnetory/venv/lib/python3.13/site-packages/google/api_core/retry/retry_base.py", line 229, in _retry_error_helper
    raise final_exc from source_exc
google.api_core.exceptions.RetryError: Timeout of 600.0s exceeded, last exception: 503 DNS resolution failed for cloudfunctions.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=cloudfunctions.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 14:01:00,019 - INFO - gcp_inventory:cloud_functions.py - [ce-ps3][cloud_function] Fetching V2 functions...
2025-06-09 14:02:15,207 - ERROR - gcp_inventory:cloud_functions.py - [ce-ps3][cloud_function] Failed to list V2 functions: 503 DNS resolution failed for cloudfunctions.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=cloudfunctions.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/gcp-inventory/Updated-gcp-invnetory/venv/lib/python3.13/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
  File "/Users/<USER>/Downloads/gcp-inventory/Updated-gcp-invnetory/venv/lib/python3.13/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ~~~~~~~~~~~~~~~^
        request,
        ^^^^^^^^
    ...<4 lines>...
        compression=compression,
        ^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/Downloads/gcp-inventory/Updated-gcp-invnetory/venv/lib/python3.13/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ~~~~~~~~~~~^^
  File "/Users/<USER>/Downloads/gcp-inventory/Updated-gcp-invnetory/venv/lib/python3.13/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/gcp-inventory/Updated-gcp-invnetory/venv/lib/python3.13/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        request,
        ^^^^^^^^
    ...<4 lines>...
        compression=new_compression,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/Downloads/gcp-inventory/Updated-gcp-invnetory/venv/lib/python3.13/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
  File "/Users/<USER>/Downloads/gcp-inventory/Updated-gcp-invnetory/venv/lib/python3.13/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for cloudfunctions.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=cloudfunctions.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for cloudfunctions.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=cloudfunctions.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/gcp-inventory/Updated-gcp-invnetory/gcp_inventory_tool/fetchers/cloud_functions.py", line 264, in fetch_resources
    functions_v2_list = v2_client.list_functions(request=request_v2)
  File "/Users/<USER>/Downloads/gcp-inventory/Updated-gcp-invnetory/venv/lib/python3.13/site-packages/google/cloud/functions_v2/services/function_service/client.py", line 1146, in list_functions
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "/Users/<USER>/Downloads/gcp-inventory/Updated-gcp-invnetory/venv/lib/python3.13/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "/Users/<USER>/Downloads/gcp-inventory/Updated-gcp-invnetory/venv/lib/python3.13/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for cloudfunctions.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=cloudfunctions.googleapis.com is_balancer=0: Timeout while contacting DNS servers
