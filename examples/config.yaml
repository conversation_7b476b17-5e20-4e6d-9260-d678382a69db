# --- File: config.yaml ---
# Example configuration file for the GCP Inventory Tool

# List of GCP Project IDs to scan.
# Ensure the authenticated principal (user or service account) has necessary permissions
# (e.g., Viewer, Compute Viewer, Storage Viewer, Pub/Sub Viewer, etc.)
# and the required APIs are enabled in these projects.
projects:
  # - "ce-ps3"
  - "prj-ali-cmn-prd-host-01"
  # Add more project IDs here

# List of services to include in the inventory.
# These keys MUST match the SERVICE_NAME attribute defined in the
# corresponding ServiceFetcher subclass (e.g., 'compute', 'storage').
# The tool will dynamically load fetchers based on these keys.
# Add/remove services based on the fetchers you have implemented and want to run.
services:
  # - "compute"                 # Compute Engine VMs & related info
  # - "storage"                 # Cloud Storage Buckets
  - "pubsub"                  # Pub/Sub Subscriptions
  # - "pubsub_topic"            # Pub/Sub Topics
  # - "gke_cluster"             # GKE Clusters
  # - "cloud_function"          # Cloud Functions (V1 & V2)
  # - "app_engine_service"      # App Engine Services & Versions
  # - "app_engine_firewall"     # App Engine Firewall Rules
  # - "route"                   # VPC Routes
  # - "firewall_rule"           # VPC Firewall Rules
  # - "vpc_network"             # VPC Networks
  # - "subnet"                  # VPC Subnets
  # - "address"                 # Compute Addresses (External/Internal IPs)
  # - "cloud_router"            # Cloud Routers (incl. BGP, NAT)
  # - "service_account"         # IAM Service Accounts
  # - "vpc_connector"           # Serverless VPC Access Connectors
  # - "project_iam"             # Project IAM Policy Bindings
  # - "image"                   # Compute Images (Custom)
  # - "ha_vpn_gateway"          # HA VPN Gateways
  # - "classic_vpn_gateway"     # Classic Target VPN Gateways
  # - "vpn_tunnel"              # VPN Tunnels
  # - "dns_managed_zone"        # Cloud DNS Managed Zones
  # - "dns_policy"              # Cloud DNS Policies
  # - "kms_key"                 # KMS Keys
  # - "instance_group_manager"  # Instance Group Managers (MIGs)
  # - "instance_template"       # Instance Templates
  # - "secret_manager"          # Secret Manager Secrets (Metadata)
  # - "security_policy"         # Compute Security Policies (Cloud Armor)
  # - "health_check"            # Compute Health Checks
  # - "ssl_certificate"         # Compute SSL Certificates
  # - "data_fusion"             # Data Fusion Instances
  # - "redis"                   # Memorystore for Redis Instances
  # - "memcached"               # Memorystore for Memcached Instances
  # - "cloud_scheduler_job"     # Cloud Scheduler Jobs
  # - "cloud_run_service"       # Cloud Run Services
  # - "bigtable"                # Bigtable Instances & Clusters
  # - "composer_environment"    # Cloud Composer Environments
  # - "dataproc_cluster"        # Dataproc Clusters
  # - "artifact_registry_repo"  # Artifact Registry Repositories
  # - "container_registry_repo" # Container Registry (via Artifact Registry)
  # - "dataflow_job"            # Dataflow Jobs
  # - "log_sink"                # Logging Sinks
  # - "alert_policy"            # Monitoring Alert Policies
  # - "interconnect_attachment" # Interconnect VLAN Attachments

# Optional: Output configuration
output:
  # Specify the desired output format ('json', 'csv', 'console', etc.)
  # This corresponds to registered OutputFormatter classes. Only 'json' is implemented by default.
  format: "excel"

  # Optional: Path to the output file.
  # If omitted, behavior depends on the formatter (e.g., JSON might print to stdout).
  # You can use relative paths (e.g., output/inventory.json) or absolute paths.
  file: "gcp_inventory_output.xlsx"

  # Optional: Dictionary of options specific to the chosen formatter.
  # format_options:
    #  json_indent: 2 # Indentation level for JSON output
    # csv_delimiter: "," # Delimiter for CSV output (if csv formatter exists)

# Optional: Logging configuration
logging:
  # Logging level for the application.
  # Options: DEBUG, INFO, WARNING, ERROR, CRITICAL
  level: "INFO"

  # Optional: Enable logging to a file (true/false). Defaults to false if omitted.
  log_to_file: true

  # Optional: Path to the log file if log_to_file is true.
  # Defaults to 'logs/app.log' in the script's directory if omitted.
  # The directory will be created if it doesn't exist.
  log_file: "inventory_run.log" # Example: log to a file in the current directory

# Optional: Execution control
execution:
  # Maximum number of concurrent threads for fetching resources.
  # Reduced from 10 to 3 to prevent DNS resolution failures and connection pool exhaustion
  # When running with high concurrency, DNS resolvers and network connections can be overwhelmed
  # Adjust based on your machine, network, and API quotas.
  # If omitted, a default based on CPU cores will be used.
  max_workers: 1

