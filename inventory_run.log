2025-06-09 17:45:12,749 - INFO - gcp_inventory:logging_config.py - Logging configured with name='gcp_inventory', level=INFO
2025-06-09 17:45:12,749 - INFO - gcp_inventory:logging_config.py - File logging enabled: /Users/<USER>/Downloads/updated-gcp-inventory/inventory_run.log
2025-06-09 17:45:12,749 - INFO - gcp_inventory:credentials.py - Attempting to get credentials using ADC with scopes: ['https://www.googleapis.com/auth/cloud-platform.read-only']
2025-06-09 17:45:13,164 - INFO - gcp_inventory:credentials.py - ADC did not detect a default project ID.
2025-06-09 17:45:13,164 - INFO - gcp_inventory:credentials.py - Successfully obtained credentials via ADC.
2025-06-09 17:45:13,164 - INFO - gcp_inventory:__init__.py - Dynamically loading service fetchers...
2025-06-09 17:45:14,798 - WARNING - gcp_inventory:__init__.py - Duplicate fetcher found for service 'vpc_connector'. Overwriting VpcConnectorFetcher with VpcConnectorFetcher.
2025-06-09 17:45:14,799 - INFO - gcp_inventory:__init__.py - Registered fetchers for services: 'address' (AddressFetcher),
        'alert_policy' (AlertPolicyFetcher),
        'app_engine_firewall' (AppEngineFirewallFetcher),
        'app_engine_service' (AppEngineServiceFetcher),
        'artifact_registry_repo' (ArtifactRegistryRepositoryFetcher),
        'backend_bucket' (BackendBucketFetcher),
        'backend_service' (BackendServiceFetcher),
        'bigtable' (BigtableFetcher),
        'classic_vpn_gateway' (ClassicVpnGatewayFetcher),
        'cloud_build_trigger' (CloudBuildTriggerFetcher),
        'cloud_function' (CloudFunctionFetcher),
        'cloud_router' (CloudRouterFetcher),
        'cloud_run_service' (CloudRunServiceFetcher),
        'cloud_scheduler_job' (CloudSchedulerJobFetcher),
        'spanner_instance' (SpannerInstanceFetcher),
        'cloud_sql' (CloudSqlInstanceFetcher),
        'storage' (CloudStorageFetcher),
        'composer_environment' (ComposerEnvironmentFetcher),
        'compute' (ComputeEngineFetcher),
        'container_registry_repo' (ContainerRegistryRepositoryFetcher),
        'data_fusion' (DataFusionFetcher),
        'vpc_connector' (VpcConnectorFetcher),
        'dataproc_cluster' (DataprocClusterFetcher),
        'dns_managed_zone' (DnsManagedZoneFetcher),
        'dns_policy' (DnsPolicyFetcher),
        'firewall_policy' (FirewallPolicyFetcher),
        'firewall_rule' (FirewallRuleFetcher),
        'forwarding_rule' (ForwardingRuleFetcher),
        'gke_cluster' (GKEClusterFetcher),
        'ha_vpn_gateway' (HaVpnGatewayFetcher),
        'health_check' (HealthCheckFetcher),
        'image' (ImageFetcher),
        'instance_group_manager' (InstanceGroupManagerFetcher),
        'instance_template' (InstanceTemplateFetcher),
        'interconnect_attachment' (InterconnectAttachmentFetcher),
        'kms_key' (KmsKeyFetcher),
        'log_sink' (LogSinkFetcher),
        'memcached' (MemcachedFetcher),
        'project_iam' (ProjectIAMFetcher),
        'redis' (RedisFetcher),
        'route' (RouteFetcher),
        'secret_manager' (SecretManagerFetcher),
        'security_policy' (SecurityPolicyFetcher),
        'service_account' (ServiceAccountFetcher),
        'ssl_certificate' (SslCertificateFetcher),
        'subnet' (SubnetFetcher),
        'pubsub' (PubSubFetcher),
        'pubsub_topic' (PubSubTopicFetcher),
        'vpc_network' (VPCNetworkFetcher),
        'vpc_connector' (VpcConnectorFetcher),
        'vpn_tunnel' (VpnTunnelFetcher)
2025-06-09 17:45:14,799 - INFO - gcp_inventory:subscriptions.py - [prj-ali-cmn-prd-host-01][pubsub] Starting Pub/Sub subscription fetch...
2025-06-09 17:46:30,062 - ERROR - gcp_inventory:subscriptions.py - [prj-ali-cmn-prd-host-01][pubsub] An unexpected error occurred while fetching Pub/Sub subscriptions: Timeout of 60.0s exceeded, last exception: 503 DNS resolution failed for pubsub.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=pubsub.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for pubsub.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=pubsub.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for pubsub.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=pubsub.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for pubsub.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=pubsub.googleapis.com is_balancer=0: Timeout while contacting DNS servers

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/subscriptions.py", line 58, in fetch_resources
    subscription_pager = subscriber_client.list_subscriptions(
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/pubsub_v1/services/subscriber/client.py", line 1303, in list_subscriptions
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 229, in _retry_error_helper
    raise final_exc from source_exc
google.api_core.exceptions.RetryError: Timeout of 60.0s exceeded, last exception: 503 DNS resolution failed for pubsub.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=pubsub.googleapis.com is_balancer=0: Timeout while contacting DNS servers
