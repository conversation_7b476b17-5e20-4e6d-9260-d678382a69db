2025-06-09 17:05:08,012 - INFO - gcp_inventory:logging_config.py - Logging configured with name='gcp_inventory', level=INFO
2025-06-09 17:05:08,013 - INFO - gcp_inventory:logging_config.py - File logging enabled: /Users/<USER>/Downloads/updated-gcp-inventory/inventory_run.log
2025-06-09 17:05:08,013 - INFO - gcp_inventory:credentials.py - Attempting to get credentials using ADC with scopes: ['https://www.googleapis.com/auth/cloud-platform.read-only']
2025-06-09 17:05:14,555 - INFO - gcp_inventory:credentials.py - ADC did not detect a default project ID.
2025-06-09 17:05:14,555 - INFO - gcp_inventory:credentials.py - Successfully obtained credentials via ADC.
2025-06-09 17:05:14,555 - INFO - gcp_inventory:__init__.py - Dynamically loading service fetchers...
2025-06-09 17:05:15,948 - WARNING - gcp_inventory:__init__.py - Duplicate fetcher found for service 'vpc_connector'. Overwriting VpcConnectorFetcher with VpcConnectorFetcher.
2025-06-09 17:05:15,948 - INFO - gcp_inventory:__init__.py - Registered fetchers for services: 'address' (AddressFetcher),
        'alert_policy' (AlertPolicyFetcher),
        'app_engine_firewall' (AppEngineFirewallFetcher),
        'app_engine_service' (AppEngineServiceFetcher),
        'artifact_registry_repo' (ArtifactRegistryRepositoryFetcher),
        'backend_bucket' (BackendBucketFetcher),
        'backend_service' (BackendServiceFetcher),
        'bigtable' (BigtableFetcher),
        'classic_vpn_gateway' (ClassicVpnGatewayFetcher),
        'cloud_build_trigger' (CloudBuildTriggerFetcher),
        'cloud_function' (CloudFunctionFetcher),
        'cloud_router' (CloudRouterFetcher),
        'cloud_run_service' (CloudRunServiceFetcher),
        'cloud_scheduler_job' (CloudSchedulerJobFetcher),
        'spanner_instance' (SpannerInstanceFetcher),
        'cloud_sql' (CloudSqlInstanceFetcher),
        'storage' (CloudStorageFetcher),
        'composer_environment' (ComposerEnvironmentFetcher),
        'compute' (ComputeEngineFetcher),
        'container_registry_repo' (ContainerRegistryRepositoryFetcher),
        'data_fusion' (DataFusionFetcher),
        'vpc_connector' (VpcConnectorFetcher),
        'dataproc_cluster' (DataprocClusterFetcher),
        'dns_managed_zone' (DnsManagedZoneFetcher),
        'dns_policy' (DnsPolicyFetcher),
        'firewall_policy' (FirewallPolicyFetcher),
        'firewall_rule' (FirewallRuleFetcher),
        'forwarding_rule' (ForwardingRuleFetcher),
        'gke_cluster' (GKEClusterFetcher),
        'ha_vpn_gateway' (HaVpnGatewayFetcher),
        'health_check' (HealthCheckFetcher),
        'image' (ImageFetcher),
        'instance_group_manager' (InstanceGroupManagerFetcher),
        'instance_template' (InstanceTemplateFetcher),
        'interconnect_attachment' (InterconnectAttachmentFetcher),
        'kms_key' (KmsKeyFetcher),
        'log_sink' (LogSinkFetcher),
        'memcached' (MemcachedFetcher),
        'project_iam' (ProjectIAMFetcher),
        'redis' (RedisFetcher),
        'route' (RouteFetcher),
        'secret_manager' (SecretManagerFetcher),
        'security_policy' (SecurityPolicyFetcher),
        'service_account' (ServiceAccountFetcher),
        'ssl_certificate' (SslCertificateFetcher),
        'subnet' (SubnetFetcher),
        'pubsub' (PubSubFetcher),
        'pubsub_topic' (PubSubTopicFetcher),
        'vpc_network' (VPCNetworkFetcher),
        'vpc_connector' (VpcConnectorFetcher),
        'vpn_tunnel' (VpnTunnelFetcher)
2025-06-09 17:05:15,948 - INFO - gcp_inventory:compute_engine.py - [ce-ps3][compute] Starting detailed VM fetch...
2025-06-09 17:05:15,948 - INFO - gcp_inventory:cloud_storage.py - [ce-ps3][storage] Starting Cloud Storage bucket fetch...
2025-06-09 17:05:15,950 - INFO - gcp_inventory:subscriptions.py - [ce-ps3][pubsub] Starting Pub/Sub subscription fetch...
2025-06-09 17:05:15,950 - INFO - gcp_inventory:topics.py - [ce-ps3][pubsub_topic] Starting Pub/Sub topic fetch...
2025-06-09 17:05:15,950 - INFO - gcp_inventory:gke.py - [ce-ps3][gke_cluster] Starting GKE cluster fetch...
2025-06-09 17:05:15,951 - INFO - gcp_inventory:cloud_functions.py - [ce-ps3][cloud_function] Starting Cloud Function fetch (V1 & V2)...
2025-06-09 17:05:15,951 - INFO - gcp_inventory:app_engine_service.py - [ce-ps3][app_engine_service] Starting App Engine service/version fetch...
2025-06-09 17:05:15,952 - INFO - gcp_inventory:cloud_functions.py - [ce-ps3][cloud_function] Fetching V1 functions...
2025-06-09 17:05:16,825 - INFO - gcp_inventory:vpc.py - [ce-ps3][vpc_network] Starting VPC Network fetch...
2025-06-09 17:05:16,825 - INFO - gcp_inventory:vpc.py - [ce-ps3][vpc_network] Fetching addresses for peering lookup...
2025-06-09 17:05:16,978 - INFO - gcp_inventory:subnet.py - [ce-ps3][subnet] Starting VPC Subnet fetch...
2025-06-09 17:05:17,648 - INFO - gcp_inventory:vpc.py - [ce-ps3][vpc_network] Finished fetching addresses.
2025-06-09 17:05:17,775 - INFO - gcp_inventory:subnet.py - [ce-ps3][subnet] Finished VPC Subnet fetch. Found 59 subnets.
2025-06-09 17:05:17,775 - INFO - gcp_inventory:address.py - [ce-ps3][address] Starting Compute Address (PIP) fetch...
2025-06-09 17:05:17,901 - INFO - gcp_inventory:compute_engine.py - [ce-ps3][compute] Iterating through zones/instances...
2025-06-09 17:05:18,255 - WARNING - gcp_inventory:vpc.py - Could not find matching VPC_PEERING address for specific peering 'servicenetworking-googleapis-com' in VPC 'ce-ps3-nikhil-vpc-01'
2025-06-09 17:05:18,256 - WARNING - gcp_inventory:vpc.py - Could not find matching VPC_PEERING address for specific peering 'servicenetworking-googleapis-com' in VPC 'default'
2025-06-09 17:05:18,257 - INFO - gcp_inventory:vpc.py - [ce-ps3][vpc_network] Finished VPC Network fetch. Found 7 networks.
2025-06-09 17:05:18,258 - INFO - gcp_inventory:cloud_router.py - [ce-ps3][cloud_router] Starting Cloud Router fetch...
2025-06-09 17:05:18,732 - INFO - gcp_inventory:address.py - [ce-ps3][address] Finished Compute Address fetch. Found 10 addresses.
2025-06-09 17:05:18,733 - INFO - gcp_inventory:service_account.py - [ce-ps3][service_account] Starting IAM Service Account fetch...
2025-06-09 17:05:19,184 - INFO - gcp_inventory:cloud_router.py - [ce-ps3][cloud_router] Finished Cloud Router fetch. Found 4 routers.
2025-06-09 17:05:19,184 - INFO - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector] Starting VPC Access Connector fetch across all regions...
2025-06-09 17:05:20,747 - INFO - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector] Will check 42 regions for VPC connectors.
2025-06-09 17:05:20,763 - INFO - gcp_inventory:service_account.py - [ce-ps3][service_account] Finished IAM Service Account fetch. Found 22 service accounts.
2025-06-09 17:05:20,763 - INFO - gcp_inventory:project_iam.py - [ce-ps3][project_iam] Starting Project IAM Policy fetch...
2025-06-09 17:05:32,208 - INFO - gcp_inventory:compute_engine.py - [ce-ps3][compute] Finished detailed VM fetch. Found 18 instances.
2025-06-09 17:05:32,209 - INFO - gcp_inventory:image.py - [ce-ps3][image] Starting Compute Image fetch...
2025-06-09 17:05:32,725 - INFO - gcp_inventory:image.py - [ce-ps3][image] Finished Compute Image fetch. Found 8 custom images.
2025-06-09 17:05:32,725 - INFO - gcp_inventory:ha_vpn_gateway.py - [ce-ps3][ha_vpn_gateway] Starting HA VPN Gateway fetch...
2025-06-09 17:05:33,526 - INFO - gcp_inventory:ha_vpn_gateway.py - [ce-ps3][ha_vpn_gateway] Finished HA VPN Gateway fetch. Found 0 gateways.
2025-06-09 17:05:33,526 - INFO - gcp_inventory:classic_vpn_gateway.py - [ce-ps3][classic_vpn_gateway] Starting Classic VPN Gateway fetch...
2025-06-09 17:05:34,293 - INFO - gcp_inventory:classic_vpn_gateway.py - [ce-ps3][classic_vpn_gateway] Finished Classic VPN Gateway fetch. Found 0 gateways.
2025-06-09 17:05:34,293 - INFO - gcp_inventory:vpn_tunnel.py - [ce-ps3][vpn_tunnel] Starting VPN Tunnel fetch...
2025-06-09 17:05:35,112 - INFO - gcp_inventory:vpn_tunnel.py - [ce-ps3][vpn_tunnel] Finished VPN Tunnel fetch. Found 0 tunnels.
2025-06-09 17:05:35,112 - INFO - gcp_inventory:dns_managed_zone.py - [ce-ps3][dns_managed_zone] Starting Cloud DNS Managed Zone fetch...
2025-06-09 17:05:35,648 - INFO - gcp_inventory:dns_managed_zone.py - [ce-ps3][dns_managed_zone] Finished Cloud DNS Managed Zone fetch. Found 4 zones.
2025-06-09 17:05:35,648 - INFO - gcp_inventory:dns_policy.py - [ce-ps3][dns_policy] Starting Cloud DNS Policy fetch...
2025-06-09 17:05:36,029 - INFO - gcp_inventory:dns_policy.py - [ce-ps3][dns_policy] Finished Cloud DNS Policy fetch. Found 0 policies.
2025-06-09 17:05:36,029 - INFO - gcp_inventory:kms_key.py - [ce-ps3][kms_key] Starting KMS Key fetch...
2025-06-09 17:05:50,194 - INFO - gcp_inventory:cloud_storage.py - [ce-ps3][storage] Finished Cloud Storage bucket fetch. Found 32 buckets.
2025-06-09 17:05:50,195 - INFO - gcp_inventory:instance_group_manager.py - [ce-ps3][instance_group_manager] Starting Instance Group Manager fetch...
2025-06-09 17:05:51,876 - INFO - gcp_inventory:instance_group_manager.py - [ce-ps3][instance_group_manager] Finished Instance Group Manager fetch. Found 3 managers.
2025-06-09 17:05:51,877 - INFO - gcp_inventory:instance_template.py - [ce-ps3][instance_template] Starting Instance Template fetch...
2025-06-09 17:05:52,370 - INFO - gcp_inventory:instance_template.py - [ce-ps3][instance_template] Finished Instance Template fetch. Found 1 templates.
2025-06-09 17:06:16,067 - ERROR - gcp_inventory:subscriptions.py - [ce-ps3][pubsub] Failed to list or process Pub/Sub subscriptions: 504 Deadline Exceeded
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/subscriptions.py", line 55, in fetch_resources
    subscription_paths = subscriber_client.list_subscriptions(request={"project": project_path})
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/pubsub_v1/services/subscriber/client.py", line 1303, in list_subscriptions
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.DeadlineExceeded: 504 Deadline Exceeded
2025-06-09 17:06:16,075 - INFO - gcp_inventory:security_policy.py - [ce-ps3][security_policy] Starting Security Policy fetch...
2025-06-09 17:06:16,850 - INFO - gcp_inventory:security_policy.py - [ce-ps3][security_policy] Finished Security Policy fetch. Found 1 policies.
2025-06-09 17:06:16,850 - INFO - gcp_inventory:health_check.py - [ce-ps3][health_check] Starting Health Check fetch...
2025-06-09 17:06:17,749 - INFO - gcp_inventory:health_check.py - [ce-ps3][health_check] Finished Health Check fetch. Found 9 health checks.
2025-06-09 17:06:17,749 - INFO - gcp_inventory:ssl_certificate.py - [ce-ps3][ssl_certificate] Starting SSL Certificate fetch...
2025-06-09 17:06:18,566 - INFO - gcp_inventory:ssl_certificate.py - [ce-ps3][ssl_certificate] Finished SSL Certificate fetch. Found 5 certificates.
2025-06-09 17:06:18,566 - INFO - gcp_inventory:data_fusion.py - [ce-ps3][data_fusion] Starting Data Fusion instance fetch...
2025-06-09 17:06:20,756 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][africa-south1] Failed to list or process VPC Access Connectors: 504 Deadline Exceeded
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.DEADLINE_EXCEEDED
	details = "Deadline Exceeded"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"Deadline Exceeded", grpc_status:4}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.DeadlineExceeded: 504 Deadline Exceeded
2025-06-09 17:06:20,768 - ERROR - gcp_inventory:project_iam.py - [ce-ps3][project_iam] Failed to get or process Project IAM Policy: 504 Deadline Exceeded
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/project_iam.py", line 46, in fetch_resources
    policy = client.get_iam_policy(resource=resource_name)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/resourcemanager_v3/services/projects/client.py", line 1983, in get_iam_policy
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.DeadlineExceeded: 504 Deadline Exceeded
2025-06-09 17:06:20,771 - INFO - gcp_inventory:redis.py - [ce-ps3][redis] Starting Redis instance fetch...
2025-06-09 17:06:31,171 - INFO - gcp_inventory:memcached.py - [ce-ps3][memcached] Starting Memcached instance fetch...
2025-06-09 17:06:31,285 - ERROR - gcp_inventory:app_engine_service.py - [ce-ps3][app_engine_service] Failed to list or process App Engine services/versions: 503 DNS resolution failed for appengine.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=appengine.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for appengine.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=appengine.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for appengine.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=appengine.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/app_engine_service.py", line 34, in fetch_resources
    services = services_client.list_services(request=request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/appengine_admin_v1/services/services/client.py", line 766, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for appengine.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=appengine.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:31,290 - INFO - gcp_inventory:cloud_scheduler.py - [ce-ps3][cloud_scheduler_job] Starting Cloud Scheduler job fetch...
2025-06-09 17:06:31,292 - INFO - gcp_inventory:cloud_scheduler.py - [ce-ps3][cloud_scheduler_job] Using location list: ['us-central1', 'us-east1', 'us-east4', 'us-west1', 'us-west2', 'europe-west1', 'europe-west2', 'europe-west3', 'asia-east1', 'asia-northeast1', 'asia-south1', 'australia-southeast1']
2025-06-09 17:06:35,978 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][asia-east1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:35,980 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][asia-east2] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:35,981 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][asia-northeast1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:35,982 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][asia-northeast2] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:35,982 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][asia-northeast3] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:35,983 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][asia-south1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:35,984 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][asia-south2] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:35,985 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][asia-southeast1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:35,986 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][asia-southeast2] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:35,987 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][australia-southeast1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:35,988 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][australia-southeast2] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:35,989 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][europe-central2] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:35,989 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][europe-north1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:35,990 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][europe-north2] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:35,990 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][europe-southwest1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:35,991 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][europe-west1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:35,992 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][europe-west10] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:35,992 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][europe-west12] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:35,993 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][europe-west2] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:35,993 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][europe-west3] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:35,994 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][europe-west4] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:35,995 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][europe-west6] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:35,995 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][europe-west8] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:35,996 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][europe-west9] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:35,996 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][me-central1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:35,997 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][me-central2] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:35,997 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][me-west1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:35,998 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][northamerica-northeast1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:35,998 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][northamerica-northeast2] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:35,999 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][northamerica-south1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:35,999 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][southamerica-east1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:36,000 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][southamerica-west1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:36,001 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][us-central1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:36,002 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][us-east1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:36,002 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][us-east4] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:36,003 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][us-east5] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:36,004 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][us-south1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:36,004 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][us-west1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:36,005 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][us-west2] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:36,005 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][us-west3] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:36,006 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][us-west4] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:06:36,006 - INFO - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector] Finished VPC Access Connector fetch. Found 0 connectors across 42 locations.
2025-06-09 17:06:36,006 - INFO - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Starting Cloud Run Service fetch...
2025-06-09 17:06:36,041 - ERROR - gcp_inventory:kms_key.py - [ce-ps3][kms_key] Failed to list or process KMS Key Rings/Keys: Timeout of 60.0s exceeded, last exception: 504 Deadline Exceeded
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.DEADLINE_EXCEEDED
	details = "Deadline Exceeded"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:4, grpc_message:"Deadline Exceeded"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.DeadlineExceeded: 504 Deadline Exceeded

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/kms_key.py", line 79, in fetch_resources
    key_rings = client.list_key_rings(parent=parent)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/kms_v1/services/key_management_service/client.py", line 942, in list_key_rings
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 229, in _retry_error_helper
    raise final_exc from source_exc
google.api_core.exceptions.RetryError: Timeout of 60.0s exceeded, last exception: 504 Deadline Exceeded
2025-06-09 17:06:36,045 - INFO - gcp_inventory:bigtable.py - [ce-ps3][bigtable] Starting Bigtable instance & cluster fetch...
2025-06-09 17:06:47,675 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'africa-south1': 504 Deadline Exceeded
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.DeadlineExceeded: 504 Deadline Exceeded
2025-06-09 17:06:52,386 - INFO - gcp_inventory:composer_environment.py - [ce-ps3][composer_environment] Starting Cloud Composer environment fetch...
2025-06-09 17:06:52,390 - INFO - gcp_inventory:composer_environment.py - [ce-ps3][composer_environment] Using location list: ['us-central1', 'us-east1', 'us-east4', 'us-west1', 'us-west2', 'europe-west1', 'europe-west2', 'europe-west3', 'asia-east1', 'asia-northeast1', 'asia-south1', 'australia-southeast1']
2025-06-09 17:06:57,686 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'asia-east1': 504 Deadline Exceeded
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.DeadlineExceeded: 504 Deadline Exceeded
2025-06-09 17:07:07,698 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'asia-east2': 504 Deadline Exceeded
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.DeadlineExceeded: 504 Deadline Exceeded
2025-06-09 17:07:17,709 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'asia-northeast1': 504 Deadline Exceeded
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.DeadlineExceeded: 504 Deadline Exceeded
2025-06-09 17:07:27,721 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'asia-northeast2': 504 Deadline Exceeded
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.DeadlineExceeded: 504 Deadline Exceeded
2025-06-09 17:07:33,785 - ERROR - gcp_inventory:data_fusion.py - [ce-ps3][data_fusion] Failed to list or process Data Fusion instances: 503 DNS resolution failed for datafusion.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=datafusion.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for datafusion.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=datafusion.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for datafusion.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=datafusion.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/data_fusion.py", line 165, in fetch_resources
    instances = client.list_instances(request=request)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/data_fusion_v1/services/data_fusion/client.py", line 945, in list_instances
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for datafusion.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=datafusion.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:07:33,790 - INFO - gcp_inventory:dataproc_cluster.py - [ce-ps3][dataproc_cluster] Starting Dataproc cluster fetch...
2025-06-09 17:07:35,115 - INFO - gcp_inventory:dataproc_cluster.py - [ce-ps3][dataproc_cluster] Will check Dataproc clusters in regions: ['africa-south1', 'asia-east1', 'asia-east2', 'asia-northeast1', 'asia-northeast2', 'asia-northeast3', 'asia-south1', 'asia-south2', 'asia-southeast1', 'asia-southeast2', 'australia-southeast1', 'australia-southeast2', 'europe-central2', 'europe-north1', 'europe-north2', 'europe-southwest1', 'europe-west1', 'europe-west10', 'europe-west12', 'europe-west2', 'europe-west3', 'europe-west4', 'europe-west6', 'europe-west8', 'europe-west9', 'me-central1', 'me-central2', 'me-west1', 'northamerica-northeast1', 'northamerica-northeast2', 'northamerica-south1', 'southamerica-east1', 'southamerica-west1', 'us-central1', 'us-east1', 'us-east4', 'us-east5', 'us-south1', 'us-west1', 'us-west2', 'us-west3', 'us-west4']
2025-06-09 17:07:35,988 - ERROR - gcp_inventory:redis.py - [ce-ps3][redis] Failed to list or process Redis instances: 503 DNS resolution failed for redis.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=redis.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for redis.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=redis.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for redis.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=redis.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/redis.py", line 54, in fetch_resources
    instances = client.list_instances(parent=parent)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/redis_v1/services/cloud_redis/client.py", line 849, in list_instances
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for redis.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=redis.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:07:35,995 - INFO - gcp_inventory:artifact_registry.py - [ce-ps3][artifact_registry_repo] Starting Artifact Registry repository fetch...
2025-06-09 17:07:36,055 - ERROR - gcp_inventory:bigtable.py - [ce-ps3][bigtable] Failed to list or process Bigtable instances: Timeout of 60.0s exceeded, last exception: 504 Deadline Exceeded
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.DEADLINE_EXCEEDED
	details = "Deadline Exceeded"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"Deadline Exceeded", grpc_status:4}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.DeadlineExceeded: 504 Deadline Exceeded

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/bigtable.py", line 66, in fetch_resources
    response = instance_client.list_instances(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/bigtable_admin_v2/services/bigtable_instance_admin/client.py", line 1188, in list_instances
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 229, in _retry_error_helper
    raise final_exc from source_exc
google.api_core.exceptions.RetryError: Timeout of 60.0s exceeded, last exception: 504 Deadline Exceeded
2025-06-09 17:07:36,063 - INFO - gcp_inventory:container_registry.py - [ce-ps3][container_registry_repo] Starting Container Registry (via Artifact Registry) fetch...
2025-06-09 17:07:37,734 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'asia-northeast3': 504 Deadline Exceeded
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.DeadlineExceeded: 504 Deadline Exceeded
2025-06-09 17:07:46,370 - ERROR - gcp_inventory:memcached.py - [ce-ps3][memcached] Failed to list or process Memcached instances: 503 DNS resolution failed for memcache.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=memcache.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for memcache.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=memcache.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for memcache.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=memcache.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/memcached.py", line 54, in fetch_resources
    instances = client.list_instances(parent=parent)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/memcache_v1/services/cloud_memcache/client.py", line 841, in list_instances
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for memcache.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=memcache.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:07:46,376 - INFO - gcp_inventory:log_sink.py - [ce-ps3][log_sink] Starting Logging Sink fetch...
2025-06-09 17:07:47,739 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'asia-south1': 504 Deadline Exceeded
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.DeadlineExceeded: 504 Deadline Exceeded
2025-06-09 17:07:56,668 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'asia-south2': Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 229, in _retry_error_helper
    raise final_exc from source_exc
google.api_core.exceptions.RetryError: Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:08:04,261 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'asia-southeast1': Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 229, in _retry_error_helper
    raise final_exc from source_exc
google.api_core.exceptions.RetryError: Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:08:07,611 - WARNING - gcp_inventory:composer_environment.py - [ce-ps3][composer_environment] Error processing location us-central1: 503 DNS resolution failed for composer.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=composer.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:08:07,613 - WARNING - gcp_inventory:composer_environment.py - [ce-ps3][composer_environment] Error processing location us-east1: 503 DNS resolution failed for composer.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=composer.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:08:07,613 - WARNING - gcp_inventory:composer_environment.py - [ce-ps3][composer_environment] Error processing location us-east4: 503 DNS resolution failed for composer.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=composer.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:08:07,614 - WARNING - gcp_inventory:composer_environment.py - [ce-ps3][composer_environment] Error processing location us-west1: 503 DNS resolution failed for composer.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=composer.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:08:07,614 - WARNING - gcp_inventory:composer_environment.py - [ce-ps3][composer_environment] Error processing location us-west2: 503 DNS resolution failed for composer.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=composer.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:08:07,615 - WARNING - gcp_inventory:composer_environment.py - [ce-ps3][composer_environment] Error processing location europe-west1: 503 DNS resolution failed for composer.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=composer.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:08:07,615 - WARNING - gcp_inventory:composer_environment.py - [ce-ps3][composer_environment] Error processing location europe-west2: 503 DNS resolution failed for composer.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=composer.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:08:07,615 - WARNING - gcp_inventory:composer_environment.py - [ce-ps3][composer_environment] Error processing location europe-west3: 503 DNS resolution failed for composer.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=composer.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:08:07,616 - WARNING - gcp_inventory:composer_environment.py - [ce-ps3][composer_environment] Error processing location asia-east1: 503 DNS resolution failed for composer.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=composer.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:08:07,616 - WARNING - gcp_inventory:composer_environment.py - [ce-ps3][composer_environment] Error processing location asia-northeast1: 503 DNS resolution failed for composer.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=composer.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:08:07,616 - WARNING - gcp_inventory:composer_environment.py - [ce-ps3][composer_environment] Error processing location asia-south1: 503 DNS resolution failed for composer.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=composer.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:08:07,616 - WARNING - gcp_inventory:composer_environment.py - [ce-ps3][composer_environment] Error processing location australia-southeast1: 503 DNS resolution failed for composer.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=composer.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:08:07,616 - INFO - gcp_inventory:composer_environment.py - [ce-ps3][composer_environment] Finished Cloud Composer environment fetch. Found 0 environments.
2025-06-09 17:08:07,617 - INFO - gcp_inventory:alert_policy.py - [ce-ps3][alert_policy] Starting Alert Policy fetch...
2025-06-09 17:08:14,086 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'asia-southeast2': Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 229, in _retry_error_helper
    raise final_exc from source_exc
google.api_core.exceptions.RetryError: Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:08:21,782 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'australia-southeast1': Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 229, in _retry_error_helper
    raise final_exc from source_exc
google.api_core.exceptions.RetryError: Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:08:30,694 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'australia-southeast2': Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 229, in _retry_error_helper
    raise final_exc from source_exc
google.api_core.exceptions.RetryError: Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:08:36,260 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'europe-central2': Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 229, in _retry_error_helper
    raise final_exc from source_exc
google.api_core.exceptions.RetryError: Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 17:08:37,627 - ERROR - gcp_inventory:alert_policy.py - [ce-ps3][alert_policy] Failed to list notification channels: 504 Deadline Exceeded
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/alert_policy.py", line 33, in _get_notification_channels
    all_channels = client.list_notification_channels(request=request)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/monitoring_v3/services/notification_channel_service/client.py", line 1140, in list_notification_channels
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.DeadlineExceeded: 504 Deadline Exceeded
