2025-06-09 16:39:40,315 - INFO - gcp_inventory:logging_config.py - Logging configured with name='gcp_inventory', level=INFO
2025-06-09 16:39:40,315 - INFO - gcp_inventory:logging_config.py - File logging enabled: /Users/<USER>/Downloads/updated-gcp-inventory/inventory_run.log
2025-06-09 16:39:40,315 - INFO - gcp_inventory:credentials.py - Attempting to get credentials using ADC with scopes: ['https://www.googleapis.com/auth/cloud-platform.read-only']
2025-06-09 16:39:40,668 - INFO - gcp_inventory:credentials.py - ADC did not detect a default project ID.
2025-06-09 16:39:40,668 - INFO - gcp_inventory:credentials.py - Successfully obtained credentials via ADC.
2025-06-09 16:39:40,668 - INFO - gcp_inventory:__init__.py - Dynamically loading service fetchers...
2025-06-09 16:39:42,597 - ERROR - gcp_inventory:__init__.py - Unexpected error loading fetchers from gcp_inventory_tool.fetchers.dataproc_cluster: unexpected indent (dataproc_cluster.py, line 238)
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/__init__.py", line 32, in _load_fetchers_dynamically
    module = importlib.import_module(module_name)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/homebrew/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 936, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1074, in get_code
  File "<frozen importlib._bootstrap_external>", line 1004, in source_to_code
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/dataproc_cluster.py", line 238
    clusters = regional_client.list_clusters(request=request)
IndentationError: unexpected indent
2025-06-09 16:39:42,814 - WARNING - gcp_inventory:__init__.py - Duplicate fetcher found for service 'vpc_connector'. Overwriting VpcConnectorFetcher with VpcConnectorFetcher.
2025-06-09 16:39:42,815 - INFO - gcp_inventory:__init__.py - Registered fetchers for services: 'address' (AddressFetcher),
        'alert_policy' (AlertPolicyFetcher),
        'app_engine_firewall' (AppEngineFirewallFetcher),
        'app_engine_service' (AppEngineServiceFetcher),
        'artifact_registry_repo' (ArtifactRegistryRepositoryFetcher),
        'backend_bucket' (BackendBucketFetcher),
        'backend_service' (BackendServiceFetcher),
        'bigtable' (BigtableFetcher),
        'classic_vpn_gateway' (ClassicVpnGatewayFetcher),
        'cloud_build_trigger' (CloudBuildTriggerFetcher),
        'cloud_function' (CloudFunctionFetcher),
        'cloud_router' (CloudRouterFetcher),
        'cloud_run_service' (CloudRunServiceFetcher),
        'cloud_scheduler_job' (CloudSchedulerJobFetcher),
        'spanner_instance' (SpannerInstanceFetcher),
        'cloud_sql' (CloudSqlInstanceFetcher),
        'storage' (CloudStorageFetcher),
        'composer_environment' (ComposerEnvironmentFetcher),
        'compute' (ComputeEngineFetcher),
        'container_registry_repo' (ContainerRegistryRepositoryFetcher),
        'data_fusion' (DataFusionFetcher),
        'vpc_connector' (VpcConnectorFetcher),
        'dns_managed_zone' (DnsManagedZoneFetcher),
        'dns_policy' (DnsPolicyFetcher),
        'firewall_policy' (FirewallPolicyFetcher),
        'firewall_rule' (FirewallRuleFetcher),
        'forwarding_rule' (ForwardingRuleFetcher),
        'gke_cluster' (GKEClusterFetcher),
        'ha_vpn_gateway' (HaVpnGatewayFetcher),
        'health_check' (HealthCheckFetcher),
        'image' (ImageFetcher),
        'instance_group_manager' (InstanceGroupManagerFetcher),
        'instance_template' (InstanceTemplateFetcher),
        'interconnect_attachment' (InterconnectAttachmentFetcher),
        'kms_key' (KmsKeyFetcher),
        'log_sink' (LogSinkFetcher),
        'memcached' (MemcachedFetcher),
        'project_iam' (ProjectIAMFetcher),
        'redis' (RedisFetcher),
        'route' (RouteFetcher),
        'secret_manager' (SecretManagerFetcher),
        'security_policy' (SecurityPolicyFetcher),
        'service_account' (ServiceAccountFetcher),
        'ssl_certificate' (SslCertificateFetcher),
        'subnet' (SubnetFetcher),
        'pubsub' (PubSubFetcher),
        'pubsub_topic' (PubSubTopicFetcher),
        'vpc_network' (VPCNetworkFetcher),
        'vpc_connector' (VpcConnectorFetcher),
        'vpn_tunnel' (VpnTunnelFetcher)
2025-06-09 16:39:42,815 - INFO - gcp_inventory:compute_engine.py - [ce-ps3][compute] Starting detailed VM fetch...
2025-06-09 16:39:42,815 - INFO - gcp_inventory:cloud_storage.py - [ce-ps3][storage] Starting Cloud Storage bucket fetch...
2025-06-09 16:39:42,817 - INFO - gcp_inventory:subscriptions.py - [ce-ps3][pubsub] Starting Pub/Sub subscription fetch...
2025-06-09 16:39:42,817 - INFO - gcp_inventory:topics.py - [ce-ps3][pubsub_topic] Starting Pub/Sub topic fetch...
2025-06-09 16:39:42,817 - INFO - gcp_inventory:gke.py - [ce-ps3][gke_cluster] Starting GKE cluster fetch...
2025-06-09 16:39:42,818 - INFO - gcp_inventory:cloud_functions.py - [ce-ps3][cloud_function] Starting Cloud Function fetch (V1 & V2)...
2025-06-09 16:39:42,818 - INFO - gcp_inventory:cloud_functions.py - [ce-ps3][cloud_function] Fetching V1 functions...
2025-06-09 16:39:42,818 - INFO - gcp_inventory:app_engine_service.py - [ce-ps3][app_engine_service] Starting App Engine service/version fetch...
2025-06-09 16:39:42,830 - ERROR - gcp_inventory:app_engine_service.py - [ce-ps3][app_engine_service] Failed to list or process App Engine services/versions: ServicesClient.list_services() got an unexpected keyword argument 'parent'
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/app_engine_service.py", line 33, in fetch_resources
    services = services_client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: ServicesClient.list_services() got an unexpected keyword argument 'parent'
2025-06-09 16:39:42,830 - INFO - gcp_inventory:vpc.py - [ce-ps3][vpc_network] Starting VPC Network fetch...
2025-06-09 16:39:42,831 - INFO - gcp_inventory:vpc.py - [ce-ps3][vpc_network] Fetching addresses for peering lookup...
2025-06-09 16:39:43,578 - INFO - gcp_inventory:subnet.py - [ce-ps3][subnet] Starting VPC Subnet fetch...
2025-06-09 16:39:43,788 - INFO - gcp_inventory:address.py - [ce-ps3][address] Starting Compute Address (PIP) fetch...
2025-06-09 16:39:43,835 - INFO - gcp_inventory:vpc.py - [ce-ps3][vpc_network] Finished fetching addresses.
2025-06-09 16:39:44,342 - WARNING - gcp_inventory:vpc.py - Could not find matching VPC_PEERING address for specific peering 'servicenetworking-googleapis-com' in VPC 'ce-ps3-nikhil-vpc-01'
2025-06-09 16:39:44,343 - WARNING - gcp_inventory:vpc.py - Could not find matching VPC_PEERING address for specific peering 'servicenetworking-googleapis-com' in VPC 'default'
2025-06-09 16:39:44,345 - INFO - gcp_inventory:vpc.py - [ce-ps3][vpc_network] Finished VPC Network fetch. Found 7 networks.
2025-06-09 16:39:44,345 - INFO - gcp_inventory:cloud_router.py - [ce-ps3][cloud_router] Starting Cloud Router fetch...
2025-06-09 16:39:44,463 - INFO - gcp_inventory:subnet.py - [ce-ps3][subnet] Finished VPC Subnet fetch. Found 59 subnets.
2025-06-09 16:39:44,463 - INFO - gcp_inventory:service_account.py - [ce-ps3][service_account] Starting IAM Service Account fetch...
2025-06-09 16:39:44,566 - INFO - gcp_inventory:address.py - [ce-ps3][address] Finished Compute Address fetch. Found 9 addresses.
2025-06-09 16:39:44,566 - INFO - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector] Starting VPC Access Connector fetch across all regions...
2025-06-09 16:39:45,181 - INFO - gcp_inventory:cloud_router.py - [ce-ps3][cloud_router] Finished Cloud Router fetch. Found 4 routers.
2025-06-09 16:39:45,181 - INFO - gcp_inventory:project_iam.py - [ce-ps3][project_iam] Starting Project IAM Policy fetch...
2025-06-09 16:39:46,159 - INFO - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector] Will check 42 regions for VPC connectors.
2025-06-09 16:39:46,454 - INFO - gcp_inventory:compute_engine.py - [ce-ps3][compute] Iterating through zones/instances...
2025-06-09 16:39:46,484 - INFO - gcp_inventory:service_account.py - [ce-ps3][service_account] Finished IAM Service Account fetch. Found 21 service accounts.
2025-06-09 16:39:46,484 - INFO - gcp_inventory:image.py - [ce-ps3][image] Starting Compute Image fetch...
2025-06-09 16:39:47,125 - INFO - gcp_inventory:image.py - [ce-ps3][image] Finished Compute Image fetch. Found 8 custom images.
2025-06-09 16:39:47,125 - INFO - gcp_inventory:ha_vpn_gateway.py - [ce-ps3][ha_vpn_gateway] Starting HA VPN Gateway fetch...
2025-06-09 16:39:47,919 - INFO - gcp_inventory:ha_vpn_gateway.py - [ce-ps3][ha_vpn_gateway] Finished HA VPN Gateway fetch. Found 0 gateways.
2025-06-09 16:39:47,919 - INFO - gcp_inventory:classic_vpn_gateway.py - [ce-ps3][classic_vpn_gateway] Starting Classic VPN Gateway fetch...
2025-06-09 16:39:48,768 - INFO - gcp_inventory:classic_vpn_gateway.py - [ce-ps3][classic_vpn_gateway] Finished Classic VPN Gateway fetch. Found 0 gateways.
2025-06-09 16:39:48,769 - INFO - gcp_inventory:vpn_tunnel.py - [ce-ps3][vpn_tunnel] Starting VPN Tunnel fetch...
2025-06-09 16:39:49,583 - INFO - gcp_inventory:vpn_tunnel.py - [ce-ps3][vpn_tunnel] Finished VPN Tunnel fetch. Found 0 tunnels.
2025-06-09 16:39:49,583 - INFO - gcp_inventory:dns_managed_zone.py - [ce-ps3][dns_managed_zone] Starting Cloud DNS Managed Zone fetch...
2025-06-09 16:39:50,194 - INFO - gcp_inventory:dns_managed_zone.py - [ce-ps3][dns_managed_zone] Finished Cloud DNS Managed Zone fetch. Found 4 zones.
2025-06-09 16:39:50,195 - INFO - gcp_inventory:dns_policy.py - [ce-ps3][dns_policy] Starting Cloud DNS Policy fetch...
2025-06-09 16:39:50,625 - INFO - gcp_inventory:dns_policy.py - [ce-ps3][dns_policy] Finished Cloud DNS Policy fetch. Found 0 policies.
2025-06-09 16:39:50,626 - INFO - gcp_inventory:kms_key.py - [ce-ps3][kms_key] Starting KMS Key fetch...
2025-06-09 16:40:02,789 - INFO - gcp_inventory:compute_engine.py - [ce-ps3][compute] Finished detailed VM fetch. Found 17 instances.
2025-06-09 16:40:02,790 - INFO - gcp_inventory:instance_group_manager.py - [ce-ps3][instance_group_manager] Starting Instance Group Manager fetch...
2025-06-09 16:40:02,835 - ERROR - gcp_inventory:gke.py - [ce-ps3][gke_cluster] Failed to list or process GKE clusters: Timeout of 20.0s exceeded, last exception: 504 Deadline Exceeded
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.DEADLINE_EXCEEDED
	details = "Deadline Exceeded"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:4, grpc_message:"Deadline Exceeded"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.DeadlineExceeded: 504 Deadline Exceeded

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/gke.py", line 43, in fetch_resources
    response = client.list_clusters(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/container_v1/services/cluster_manager/client.py", line 883, in list_clusters
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 229, in _retry_error_helper
    raise final_exc from source_exc
google.api_core.exceptions.RetryError: Timeout of 20.0s exceeded, last exception: 504 Deadline Exceeded
2025-06-09 16:40:02,842 - INFO - gcp_inventory:instance_template.py - [ce-ps3][instance_template] Starting Instance Template fetch...
2025-06-09 16:40:03,355 - INFO - gcp_inventory:instance_template.py - [ce-ps3][instance_template] Finished Instance Template fetch. Found 1 templates.
2025-06-09 16:40:05,399 - INFO - gcp_inventory:instance_group_manager.py - [ce-ps3][instance_group_manager] Finished Instance Group Manager fetch. Found 3 managers.
2025-06-09 16:40:05,400 - INFO - gcp_inventory:security_policy.py - [ce-ps3][security_policy] Starting Security Policy fetch...
2025-06-09 16:40:06,293 - INFO - gcp_inventory:security_policy.py - [ce-ps3][security_policy] Finished Security Policy fetch. Found 1 policies.
2025-06-09 16:40:06,293 - INFO - gcp_inventory:health_check.py - [ce-ps3][health_check] Starting Health Check fetch...
2025-06-09 16:40:07,201 - INFO - gcp_inventory:health_check.py - [ce-ps3][health_check] Finished Health Check fetch. Found 9 health checks.
2025-06-09 16:40:07,202 - INFO - gcp_inventory:ssl_certificate.py - [ce-ps3][ssl_certificate] Starting SSL Certificate fetch...
2025-06-09 16:40:08,124 - INFO - gcp_inventory:ssl_certificate.py - [ce-ps3][ssl_certificate] Finished SSL Certificate fetch. Found 5 certificates.
2025-06-09 16:40:08,125 - INFO - gcp_inventory:data_fusion.py - [ce-ps3][data_fusion] Starting Data Fusion instance fetch...
2025-06-09 16:40:11,458 - INFO - gcp_inventory:cloud_storage.py - [ce-ps3][storage] Finished Cloud Storage bucket fetch. Found 31 buckets.
2025-06-09 16:40:11,458 - INFO - gcp_inventory:redis.py - [ce-ps3][redis] Starting Redis instance fetch...
2025-06-09 16:40:42,838 - ERROR - gcp_inventory:topics.py - [ce-ps3][pubsub_topic] Failed to list or process Pub/Sub topics: 504 Deadline Exceeded
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/topics.py", line 33, in fetch_resources
    topics = publisher_client.list_topics(request={"project": project_path})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/pubsub_v1/services/publisher/client.py", line 1332, in list_topics
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.DeadlineExceeded: 504 Deadline Exceeded
2025-06-09 16:40:42,838 - ERROR - gcp_inventory:subscriptions.py - [ce-ps3][pubsub] Failed to list or process Pub/Sub subscriptions: 504 Deadline Exceeded
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/subscriptions.py", line 55, in fetch_resources
    subscription_paths = subscriber_client.list_subscriptions(request={"project": project_path})
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/pubsub_v1/services/subscriber/client.py", line 1303, in list_subscriptions
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.DeadlineExceeded: 504 Deadline Exceeded
2025-06-09 16:40:42,843 - INFO - gcp_inventory:memcached.py - [ce-ps3][memcached] Starting Memcached instance fetch...
2025-06-09 16:40:42,845 - INFO - gcp_inventory:cloud_scheduler.py - [ce-ps3][cloud_scheduler_job] Starting Cloud Scheduler job fetch...
2025-06-09 16:40:42,847 - INFO - gcp_inventory:cloud_scheduler.py - [ce-ps3][cloud_scheduler_job] Using location list: ['us-central1', 'us-east1', 'us-east4', 'us-west1', 'us-west2', 'europe-west1', 'europe-west2', 'europe-west3', 'asia-east1', 'asia-northeast1', 'asia-south1', 'australia-southeast1']
2025-06-09 16:40:45,186 - ERROR - gcp_inventory:project_iam.py - [ce-ps3][project_iam] Failed to get or process Project IAM Policy: 504 Deadline Exceeded
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/project_iam.py", line 46, in fetch_resources
    policy = client.get_iam_policy(resource=resource_name)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/resourcemanager_v3/services/projects/client.py", line 1983, in get_iam_policy
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.DeadlineExceeded: 504 Deadline Exceeded
2025-06-09 16:40:45,189 - INFO - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Starting Cloud Run Service fetch...
2025-06-09 16:40:46,164 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][africa-south1] Failed to list or process VPC Access Connectors: 504 Deadline Exceeded
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.DEADLINE_EXCEEDED
	details = "Deadline Exceeded"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:4, grpc_message:"Deadline Exceeded"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.DeadlineExceeded: 504 Deadline Exceeded
2025-06-09 16:40:50,633 - ERROR - gcp_inventory:kms_key.py - [ce-ps3][kms_key] Failed to list or process KMS Key Rings/Keys: Timeout of 60.0s exceeded, last exception: 504 Deadline Exceeded
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.DEADLINE_EXCEEDED
	details = "Deadline Exceeded"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"Deadline Exceeded", grpc_status:4}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.DeadlineExceeded: 504 Deadline Exceeded

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/kms_key.py", line 79, in fetch_resources
    key_rings = client.list_key_rings(parent=parent)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/kms_v1/services/key_management_service/client.py", line 942, in list_key_rings
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 229, in _retry_error_helper
    raise final_exc from source_exc
google.api_core.exceptions.RetryError: Timeout of 60.0s exceeded, last exception: 504 Deadline Exceeded
2025-06-09 16:40:50,638 - INFO - gcp_inventory:bigtable.py - [ce-ps3][bigtable] Starting Bigtable instance & cluster fetch...
2025-06-09 16:40:57,074 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'africa-south1': 504 Deadline Exceeded
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.DeadlineExceeded: 504 Deadline Exceeded
2025-06-09 16:40:58,021 - INFO - gcp_inventory:composer_environment.py - [ce-ps3][composer_environment] Starting Cloud Composer environment fetch...
2025-06-09 16:40:58,022 - INFO - gcp_inventory:composer_environment.py - [ce-ps3][composer_environment] Using location list: ['us-central1', 'us-east1', 'us-east4', 'us-west1', 'us-west2', 'europe-west1', 'europe-west2', 'europe-west3', 'asia-east1', 'asia-northeast1', 'asia-south1', 'australia-southeast1']
2025-06-09 16:41:01,373 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][asia-east1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,376 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][asia-east2] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,377 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][asia-northeast1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,379 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][asia-northeast2] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,380 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][asia-northeast3] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,381 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][asia-south1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,383 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][asia-south2] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,384 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][asia-southeast1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,385 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][asia-southeast2] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,386 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][australia-southeast1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,387 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][australia-southeast2] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,388 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][europe-central2] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,389 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][europe-north1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,390 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][europe-north2] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,390 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][europe-southwest1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,391 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][europe-west1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,392 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][europe-west10] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,392 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][europe-west12] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,393 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][europe-west2] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,395 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][europe-west3] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,395 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][europe-west4] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,396 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][europe-west6] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,396 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][europe-west8] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,397 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][europe-west9] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,398 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][me-central1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,398 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][me-central2] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,399 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][me-west1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,399 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][northamerica-northeast1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,400 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][northamerica-northeast2] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,401 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][northamerica-south1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,402 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][southamerica-east1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,528 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][southamerica-west1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,528 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][us-central1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,529 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][us-east1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,529 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][us-east4] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,530 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][us-east5] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,530 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][us-south1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,530 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][us-west1] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,530 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][us-west2] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,531 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][us-west3] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,531 - ERROR - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector][us-west4] Failed to list or process VPC Access Connectors: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/vpc_connector.py", line 170, in fetch_resources
    connectors = client.list_connectors(parent=parent)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/vpcaccess_v1/services/vpc_access_service/client.py", line 1079, in list_connectors
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for vpcaccess.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=vpcaccess.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:01,531 - INFO - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector] Finished VPC Access Connector fetch. Found 0 connectors across 42 locations.
2025-06-09 16:41:01,531 - INFO - gcp_inventory:artifact_registry.py - [ce-ps3][artifact_registry_repo] Starting Artifact Registry repository fetch...
2025-06-09 16:41:03,371 - INFO - gcp_inventory:container_registry.py - [ce-ps3][container_registry_repo] Starting Container Registry (via Artifact Registry) fetch...
2025-06-09 16:41:07,083 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'asia-east1': 504 Deadline Exceeded
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.DeadlineExceeded: 504 Deadline Exceeded
2025-06-09 16:41:17,091 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'asia-east2': 504 Deadline Exceeded
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.DeadlineExceeded: 504 Deadline Exceeded
2025-06-09 16:41:23,365 - ERROR - gcp_inventory:data_fusion.py - [ce-ps3][data_fusion] Failed to list or process Data Fusion instances: 503 DNS resolution failed for datafusion.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=datafusion.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for datafusion.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=datafusion.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for datafusion.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=datafusion.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/data_fusion.py", line 165, in fetch_resources
    instances = client.list_instances(request=request)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/data_fusion_v1/services/data_fusion/client.py", line 945, in list_instances
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for datafusion.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=datafusion.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:23,371 - INFO - gcp_inventory:log_sink.py - [ce-ps3][log_sink] Starting Logging Sink fetch...
2025-06-09 16:41:26,663 - ERROR - gcp_inventory:redis.py - [ce-ps3][redis] Failed to list or process Redis instances: 503 DNS resolution failed for redis.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=redis.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for redis.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=redis.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for redis.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=redis.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/redis.py", line 54, in fetch_resources
    instances = client.list_instances(parent=parent)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/redis_v1/services/cloud_redis/client.py", line 849, in list_instances
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for redis.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=redis.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:41:26,669 - INFO - gcp_inventory:alert_policy.py - [ce-ps3][alert_policy] Starting Alert Policy fetch...
2025-06-09 16:41:27,100 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'asia-northeast1': 504 Deadline Exceeded
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.DeadlineExceeded: 504 Deadline Exceeded
2025-06-09 16:41:37,111 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'asia-northeast2': 504 Deadline Exceeded
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.DeadlineExceeded: 504 Deadline Exceeded
2025-06-09 16:41:47,120 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'asia-northeast3': 504 Deadline Exceeded
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.DeadlineExceeded: 504 Deadline Exceeded
2025-06-09 16:41:50,646 - ERROR - gcp_inventory:bigtable.py - [ce-ps3][bigtable] Failed to list or process Bigtable instances: Timeout of 60.0s exceeded, last exception: 504 Deadline Exceeded
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.DEADLINE_EXCEEDED
	details = "Deadline Exceeded"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"Deadline Exceeded", grpc_status:4}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.DeadlineExceeded: 504 Deadline Exceeded

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/bigtable.py", line 66, in fetch_resources
    response = instance_client.list_instances(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/bigtable_admin_v2/services/bigtable_instance_admin/client.py", line 1188, in list_instances
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 229, in _retry_error_helper
    raise final_exc from source_exc
google.api_core.exceptions.RetryError: Timeout of 60.0s exceeded, last exception: 504 Deadline Exceeded
2025-06-09 16:41:50,651 - INFO - gcp_inventory:interconnect.py - [ce-ps3][interconnect_attachment] Starting Interconnect Attachment fetch...
2025-06-09 16:41:51,448 - INFO - gcp_inventory:interconnect.py - [ce-ps3][interconnect_attachment] Finished Interconnect Attachment fetch. Found 0 attachments.
2025-06-09 16:41:56,679 - ERROR - gcp_inventory:alert_policy.py - [ce-ps3][alert_policy] Failed to list notification channels: 504 Deadline Exceeded
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/alert_policy.py", line 33, in _get_notification_channels
    all_channels = client.list_notification_channels(request=request)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/monitoring_v3/services/notification_channel_service/client.py", line 1140, in list_notification_channels
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.DeadlineExceeded: 504 Deadline Exceeded
2025-06-09 16:41:57,131 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'asia-south1': 504 Deadline Exceeded
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.DeadlineExceeded: 504 Deadline Exceeded
2025-06-09 16:41:58,057 - ERROR - gcp_inventory:memcached.py - [ce-ps3][memcached] Failed to list or process Memcached instances: 503 DNS resolution failed for memcache.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=memcache.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for memcache.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=memcache.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for memcache.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=memcache.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/memcached.py", line 54, in fetch_resources
    instances = client.list_instances(parent=parent)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/memcache_v1/services/cloud_memcache/client.py", line 841, in list_instances
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for memcache.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=memcache.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:42:05,497 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'asia-south2': Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 229, in _retry_error_helper
    raise final_exc from source_exc
google.api_core.exceptions.RetryError: Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:42:13,147 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'asia-southeast1': Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 229, in _retry_error_helper
    raise final_exc from source_exc
google.api_core.exceptions.RetryError: Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:42:13,228 - WARNING - gcp_inventory:composer_environment.py - [ce-ps3][composer_environment] Error processing location us-central1: 503 DNS resolution failed for composer.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=composer.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:42:13,229 - WARNING - gcp_inventory:composer_environment.py - [ce-ps3][composer_environment] Error processing location us-east1: 503 DNS resolution failed for composer.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=composer.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:42:13,229 - WARNING - gcp_inventory:composer_environment.py - [ce-ps3][composer_environment] Error processing location us-east4: 503 DNS resolution failed for composer.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=composer.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:42:13,229 - WARNING - gcp_inventory:composer_environment.py - [ce-ps3][composer_environment] Error processing location us-west1: 503 DNS resolution failed for composer.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=composer.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:42:13,229 - WARNING - gcp_inventory:composer_environment.py - [ce-ps3][composer_environment] Error processing location us-west2: 503 DNS resolution failed for composer.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=composer.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:42:13,229 - WARNING - gcp_inventory:composer_environment.py - [ce-ps3][composer_environment] Error processing location europe-west1: 503 DNS resolution failed for composer.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=composer.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:42:13,230 - WARNING - gcp_inventory:composer_environment.py - [ce-ps3][composer_environment] Error processing location europe-west2: 503 DNS resolution failed for composer.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=composer.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:42:13,230 - WARNING - gcp_inventory:composer_environment.py - [ce-ps3][composer_environment] Error processing location europe-west3: 503 DNS resolution failed for composer.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=composer.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:42:13,230 - WARNING - gcp_inventory:composer_environment.py - [ce-ps3][composer_environment] Error processing location asia-east1: 503 DNS resolution failed for composer.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=composer.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:42:13,230 - WARNING - gcp_inventory:composer_environment.py - [ce-ps3][composer_environment] Error processing location asia-northeast1: 503 DNS resolution failed for composer.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=composer.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:42:13,231 - WARNING - gcp_inventory:composer_environment.py - [ce-ps3][composer_environment] Error processing location asia-south1: 503 DNS resolution failed for composer.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=composer.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:42:13,231 - WARNING - gcp_inventory:composer_environment.py - [ce-ps3][composer_environment] Error processing location australia-southeast1: 503 DNS resolution failed for composer.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=composer.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:42:13,231 - INFO - gcp_inventory:composer_environment.py - [ce-ps3][composer_environment] Finished Cloud Composer environment fetch. Found 0 environments.
2025-06-09 16:42:16,780 - ERROR - gcp_inventory:artifact_registry.py - [ce-ps3][artifact_registry_repo] Failed to list or process Artifact Registry repositories: 503 DNS resolution failed for artifactregistry.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=artifactregistry.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for artifactregistry.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=artifactregistry.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for artifactregistry.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=artifactregistry.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/artifact_registry.py", line 53, in fetch_resources
    repositories = client.list_repositories(parent=parent)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/artifactregistry_v1/services/artifact_registry/client.py", line 2337, in list_repositories
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for artifactregistry.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=artifactregistry.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:42:18,633 - ERROR - gcp_inventory:container_registry.py - [ce-ps3][container_registry_repo] Failed to list or process Artifact Registry repositories for GCR check: 503 DNS resolution failed for artifactregistry.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=artifactregistry.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for artifactregistry.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=artifactregistry.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for artifactregistry.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=artifactregistry.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/container_registry.py", line 57, in fetch_resources
    repositories = client.list_repositories(parent=parent)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/artifactregistry_v1/services/artifact_registry/client.py", line 2337, in list_repositories
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for artifactregistry.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=artifactregistry.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:42:22,045 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'asia-southeast2': Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 229, in _retry_error_helper
    raise final_exc from source_exc
google.api_core.exceptions.RetryError: Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:42:23,379 - ERROR - gcp_inventory:log_sink.py - [ce-ps3][log_sink] Failed to list or process Logging Sinks: Timeout of 60.0s exceeded, last exception: 504 Deadline Exceeded
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.DEADLINE_EXCEEDED
	details = "Deadline Exceeded"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:4, grpc_message:"Deadline Exceeded"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.DeadlineExceeded: 504 Deadline Exceeded

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/log_sink.py", line 28, in fetch_resources
    sinks = client.list_sinks(parent=parent)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/logging_v2/services/config_service_v2/client.py", line 2136, in list_sinks
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 229, in _retry_error_helper
    raise final_exc from source_exc
google.api_core.exceptions.RetryError: Timeout of 60.0s exceeded, last exception: 504 Deadline Exceeded
2025-06-09 16:42:26,689 - ERROR - gcp_inventory:alert_policy.py - [ce-ps3][alert_policy] Failed to list or process Alert Policies: 504 Deadline Exceeded
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/alert_policy.py", line 133, in fetch_resources
    policies = policy_client.list_alert_policies(name=project_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/monitoring_v3/services/alert_policy_service/client.py", line 860, in list_alert_policies
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.DeadlineExceeded: 504 Deadline Exceeded
2025-06-09 16:42:31,342 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'australia-southeast1': Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 229, in _retry_error_helper
    raise final_exc from source_exc
google.api_core.exceptions.RetryError: Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:42:38,625 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'australia-southeast2': Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 229, in _retry_error_helper
    raise final_exc from source_exc
google.api_core.exceptions.RetryError: Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:42:47,809 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'europe-central2': Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 229, in _retry_error_helper
    raise final_exc from source_exc
google.api_core.exceptions.RetryError: Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:42:57,271 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'europe-north1': Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 229, in _retry_error_helper
    raise final_exc from source_exc
google.api_core.exceptions.RetryError: Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:43:04,184 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'europe-north2': Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 229, in _retry_error_helper
    raise final_exc from source_exc
google.api_core.exceptions.RetryError: Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:43:12,211 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'europe-southwest1': Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 229, in _retry_error_helper
    raise final_exc from source_exc
google.api_core.exceptions.RetryError: Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:43:18,071 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'europe-west1': Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 229, in _retry_error_helper
    raise final_exc from source_exc
google.api_core.exceptions.RetryError: Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:43:27,982 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'europe-west10': Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 229, in _retry_error_helper
    raise final_exc from source_exc
google.api_core.exceptions.RetryError: Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:43:36,516 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'europe-west12': Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:"DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 229, in _retry_error_helper
    raise final_exc from source_exc
google.api_core.exceptions.RetryError: Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:43:46,495 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'europe-west2': Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 229, in _retry_error_helper
    raise final_exc from source_exc
google.api_core.exceptions.RetryError: Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:43:53,502 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'europe-west3': Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 229, in _retry_error_helper
    raise final_exc from source_exc
google.api_core.exceptions.RetryError: Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:44:02,886 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'europe-west4': Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 229, in _retry_error_helper
    raise final_exc from source_exc
google.api_core.exceptions.RetryError: Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
2025-06-09 16:44:10,475 - ERROR - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Failed to list or process Cloud Run services in region 'europe-west6': Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers", grpc_status:14}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ServiceUnavailable: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/updated-gcp-inventory/gcp_inventory_tool/fetchers/cloud_run_service.py", line 209, in fetch_resources
    services = client.list_services(parent=parent)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/cloud/run_v2/services/services/client.py", line 1319, in list_services
    response = rpc(
               ^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/updated-gcp-inventory/.venv/lib/python3.11/site-packages/google/api_core/retry/retry_base.py", line 229, in _retry_error_helper
    raise final_exc from source_exc
google.api_core.exceptions.RetryError: Timeout of 10.0s exceeded, last exception: 503 DNS resolution failed for run.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=A name=run.googleapis.com is_balancer=0: Timeout while contacting DNS servers
