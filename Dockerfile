# Use Python 3.11 slim image as base
FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV GOOGLE_APPLICATION_CREDENTIALS=/app/credentials/service-account.json

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Create directories for credentials and output
RUN mkdir -p /app/credentials /app/output /app/logs

# Copy requirements first for better Docker layer caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy the entire project
COPY . .

# Create a non-root user for security
RUN useradd --create-home --shell /bin/bash appuser && \
    chown -R appuser:appuser /app
USER appuser

# Set the default command
CMD ["python", "-m", "gcp_inventory_tool", "--config", "/app/config/config.yaml"]

# Expose any ports if needed (not required for this tool)
# EXPOSE 8080

# Health check (optional)
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import gcp_inventory_tool; print('OK')" || exit 1

# Labels for metadata
LABEL maintainer="GCP Inventory Tool"
LABEL description="Containerized GCP resource inventory tool"
LABEL version="1.0"
