# --- File: gcp_inventory_tool/fetchers/app_engine_service.py ---
import logging
from typing import List, Dict, Any, Optional
from google.oauth2.credentials import Credentials
from google.cloud import appengine_admin_v1
from google.api_core import exceptions as api_exceptions

from ..utils.duration_to_second import duration_to_seconds_str
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')


class AppEngineServiceFetcher(ServiceFetcher):
    """
    Fetches Google App Engine service and version details for a project.
    """
    SERVICE_NAME = "app_engine_service" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all App Engine services and their versions in the project.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting App Engine service/version fetch...")
        inventory = []
        services_client = appengine_admin_v1.ServicesClient(credentials=credentials)
        versions_client = appengine_admin_v1.VersionsClient(credentials=credentials)
        # Parent format for App Engine Admin API calls
        parent = f"apps/{project_id}"

        try:
            # List all services
            services = services_client.list_services(parent=parent)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing services...")

            for service in services:
                service_id = service.id
                logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Fetching versions for service '{service_id}'...")
                try:
                    # List all versions for the current service
                    versions = versions_client.list_versions(parent=f"{parent}/services/{service_id}")

                    for version in versions:
                        # Prepare info dictionary based on PowerShell script's fields
                        # Note: PowerShell script seemed to only take the last version [-1]
                        # This implementation iterates through ALL versions.
                        info = {
                            "ProjectID": project_id,
                            "Name": service_id, # Service Name/ID
                            "VersionId": version.id, # Version ID
                            "Environment": str(version.env), # STANDARD or FLEXIBLE
                            # "EnvironmentName": None, # PS script field - seems redundant with 'Environment'
                            # "EnvironmentValue": None, # PS script field - likely meant env_variables
                            "CreationDate": version.create_time.isoformat() if version.create_time else None,
                            "LastDeployed": version.deploy_time.isoformat() if version.deploy_time else None, # V2 field name
                            "TrafficSplit": service.split.allocations.get(version.id) if service.split else None, # Get split for this version
                            "CreatedBy": version.created_by,
                            "LivelinessCheck": {}, # Populated below
                            "ReadinessCheck": {}, # Populated below
                            "ManualScalingInstances": None, # Populated below
                            "AutomaticScaling": {}, # Populated below
                            "BasicScaling": {}, # Populated below
                            "CPU": None, # Populated below
                            "MemoryGB": None, # Populated below
                            "DiskGB": None, # Populated below
                            "RunTime": version.runtime,
                            "RuntimeAPIVersion": version.runtime_api_version,
                            "ServingStatus": str(version.serving_status),
                            "ThreadSafe": version.threadsafe,
                            "VersionURL": version.version_url,
                            "EnvVariables": dict(version.env_variables) if version.env_variables else {},
                            "BuildEnvVariables": dict(version.build_env_variables) if version.build_env_variables else {},
                            "Handlers": [], # Populated below
                            "service": self.SERVICE_NAME
                        }

                        # Scaling settings
                        if version.manual_scaling:
                            info["ManualScalingInstances"] = version.manual_scaling.instances
                        if version.automatic_scaling:
                             # Extract relevant automatic scaling settings
                             asc = version.automatic_scaling
                             info["AutomaticScaling"] = {
                                 "cool_down_period": duration_to_seconds_str(asc.cool_down_period),
                                 "cpu_utilization_target": asc.cpu_utilization.target_utilization if asc.cpu_utilization else None,
                                 "min_idle_instances": asc.standard_scheduler_settings.min_instances if asc.standard_scheduler_settings else None, # Standard specific
                                 "max_idle_instances": asc.standard_scheduler_settings.max_instances if asc.standard_scheduler_settings else None, # Standard specific
                                 "min_pending_latency": duration_to_seconds_str(asc.request_utilization.target_request_count_per_second if asc.request_utilization else None), # Standard specific
                                 "max_pending_latency": duration_to_seconds_str(asc.request_utilization.target_concurrent_requests if asc.request_utilization else None), # Standard specific
                                 # Add more fields as needed (disk, network util, custom metrics)
                             }
                        if version.basic_scaling:
                             info["BasicScaling"] = {
                                 "max_instances": version.basic_scaling.max_instances,
                                 "idle_timeout": duration_to_seconds_str(version.basic_scaling.idle_timeout)
                             }


                        # Health Checks
                        if version.liveness_check:
                            lc = version.liveness_check
                            info["LivelinessCheck"] = {
                                "path": lc.path, "host": lc.host, "timeout": duration_to_seconds_str(lc.timeout),
                                "checkInterval": duration_to_seconds_str(lc.check_interval),
                                "failureThreshold": lc.failure_threshold, "successThreshold": lc.success_threshold,
                                "initialDelay": duration_to_seconds_str(lc.initial_delay)
                            }
                        if version.readiness_check:
                             rc = version.readiness_check
                             info["ReadinessCheck"] = {
                                 "path": rc.path, "host": rc.host, "timeout": duration_to_seconds_str(rc.timeout),
                                 "checkInterval": duration_to_seconds_str(rc.check_interval),
                                 "failureThreshold": rc.failure_threshold, "successThreshold": rc.success_threshold,
                                 "appStartTimeout": duration_to_seconds_str(rc.app_start_timeout)
                             }

                        # Resources (Flex only typically)
                        if version.resources:
                            info["CPU"] = version.resources.cpu
                            info["MemoryGB"] = version.resources.memory_gb
                            info["DiskGB"] = version.resources.disk_gb
                            # Add volumes if needed: version.resources.volumes

                        # Handlers (Standard only typically)
                        if version.handlers:
                             info["Handlers"] = [
                                 {"urlRegex": h.url_regex, "scriptPath": h.script.script_path if h.script else None,
                                  "securityLevel": str(h.security_level), "authFailAction": str(h.auth_fail_action)}
                                 # Add static_files, login requirement etc. if needed
                                 for h in version.handlers
                             ]

                        inventory.append(info)

                except api_exceptions.Forbidden as e:
                    logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing versions for service '{service_id}': {e}")
                    # Continue to next service
                except Exception as e:
                    logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list versions for service '{service_id}': {e}", exc_info=True)
                    # Continue to next service

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing services or accessing App Engine Admin API. Ensure API is enabled and necessary roles granted. Details: {e}")
            return [] # Return empty list on permission errors for the service
        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] App Engine application might not exist in this project or API not enabled. Details: {e}")
             return [] # App not found or API issue
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process App Engine services/versions: {e}", exc_info=True)
            return [] # Fail gracefully for this service
        finally:
            # Ensure clients are closed
            if services_client:
                services_client.transport.close()
            if versions_client:
                versions_client.transport.close()

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished App Engine service/version fetch. Found {len(inventory)} versions across services.")
        return inventory

