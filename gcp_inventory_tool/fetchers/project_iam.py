# --- File: gcp_inventory_tool/fetchers/project_iam.py ---
import logging
import re
from typing import List, Dict, Any, Optional
from collections import defaultdict
from google.oauth2.credentials import Credentials
from google.cloud import resourcemanager_v3
from google.api_core import exceptions as api_exceptions

from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')

# Regex to remove common IAM member prefixes
MEMBER_PREFIX_RE = re.compile(r"^(serviceAccount:|user:|group:|domain:|projectOwner:|projectEditor:|projectViewer:)")

def _clean_member_name(member: str) -> str:
    """Removes standard prefixes (user:, group:, serviceAccount:) from an IAM member string."""
    return MEMBER_PREFIX_RE.sub("", member, count=1)

def _clean_role_name(role: str) -> str:
    """Removes 'roles/' prefix from an IAM role string."""
    if role.startswith("roles/"):
        return role[len("roles/"):]
    return role

class ProjectIAMFetcher(ServiceFetcher):
    """
    Fetches the IAM policy for a project and aggregates roles per member.
    """
    SERVICE_NAME = "project_iam" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches the IAM policy for the specified project and returns a list
        of members with their aggregated roles.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Project IAM Policy fetch...")
        inventory = []
        client = resourcemanager_v3.ProjectsClient(credentials=credentials)
        resource_name = f"projects/{project_id}"

        try:
            # Get the IAM policy for the project
            # Note: Requires 'resourcemanager.projects.getIamPolicy' permission
            policy = client.get_iam_policy(resource=resource_name)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing IAM policy bindings...")

            # Dictionary to aggregate roles per member
            # Key: cleaned member name, Value: set of cleaned role names
            member_roles = defaultdict(set)

            # Process bindings to aggregate roles
            for binding in policy.bindings:
                clean_role = _clean_role_name(binding.role)
                for member in binding.members:
                    clean_member = _clean_member_name(member)
                    member_roles[clean_member].add(clean_role)

            # Transform the aggregated data into the desired output format
            for member, roles in member_roles.items():
                info = {
                    "ProjectID": project_id,
                    "Member": member,
                    "Roles": sorted(list(roles)), # Return roles as a sorted list
                    # "RolesJoined": " ; ".join(sorted(list(roles))), # Optional: if joined string needed
                    "PolicyVersion": policy.version, # Added policy version
                    "PolicyEtag": policy.etag.decode('utf-8') if policy.etag else None, # Added etag
                    "service": self.SERVICE_NAME
                }
                inventory.append(info)

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied getting IAM policy. Ensure necessary roles granted (e.g., 'Security Reviewer', 'resourcemanager.projects.getIamPolicy' permission). Details: {e}")
            return [] # Return empty list on permission errors
        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Project not found or Cloud Resource Manager API might not be enabled. Details: {e}")
             return []
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to get or process Project IAM Policy: {e}", exc_info=True)
            return [] # Fail gracefully for other errors
        finally:
            try:
                client.transport.close()
            except Exception:
                pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Project IAM Policy fetch. Found {len(inventory)} members with roles.")
        return inventory
