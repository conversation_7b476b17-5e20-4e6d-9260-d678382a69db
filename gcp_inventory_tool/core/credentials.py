import google.auth
from google.oauth2.credentials import Credentials
import logging
from typing import List, Optional

logger = logging.getLogger('gcp_inventory')

# Default scopes requesting read-only access across GCP
# Adjust if more specific or different scopes are needed by fetchers.
DEFAULT_SCOPES = ['https://www.googleapis.com/auth/cloud-platform.read-only']

def get_credentials(scopes: Optional[List[str]] = None) -> Credentials:
    """
    Obtains GCP credentials using the Application Default Credentials (ADC) strategy.

    This is the recommended way to authenticate in most GCP environments.
    It automatically finds credentials from:
    1. `GOOGLE_APPLICATION_CREDENTIALS` environment variable.
    2. User credentials set via `gcloud auth application-default login`.
    3. GCP metadata server (on GCE, GKE, Cloud Functions, etc.).

    Args:
        scopes: A list of OAuth 2.0 scopes required for the API calls.
                Defaults to read-only cloud-platform scope.

    Returns:
        A google.oauth2.credentials.Credentials object ready for use.

    Raises:
        google.auth.exceptions.DefaultCredentialsError: If ADC lookup fails.
        Exception: For other unexpected errors during credential retrieval.
    """
    requested_scopes = scopes or DEFAULT_SCOPES
    try:
        logger.info(f"Attempting to get credentials using ADC with scopes: {requested_scopes}")
        # google.auth.default() finds the best available credentials
        credentials, detected_project_id = google.auth.default(scopes=requested_scopes)

        # Log the detected project ID (useful for debugging, but the inventory
        # process should rely on the projects listed in the config file).
        if detected_project_id:
            logger.info(f"ADC detected default project ID: {detected_project_id}")
        else:
            logger.info("ADC did not detect a default project ID.")

        # Optional: Check if credentials need refreshing (useful for long-running processes)
        # if credentials.expired and credentials.refresh_token:
        #     try:
        #         logger.info("Credentials expired, attempting refresh.")
        #         credentials.refresh(google.auth.transport.requests.Request())
        #     except Exception as refresh_err:
        #         logger.warning(f"Failed to refresh credentials: {refresh_err}")
        #         # Decide whether to proceed with potentially expired credentials
        #         # or raise an error depending on the application's needs.

        logger.info("Successfully obtained credentials via ADC.")
        return credentials

    except google.auth.exceptions.DefaultCredentialsError as e:
        logger.error(f"Could not find default credentials (ADC): {e}")
        logger.error(
            "Ensure you have run 'gcloud auth application-default login' "
            "or set the GOOGLE_APPLICATION_CREDENTIALS environment variable, "
            "or that the application is running in a GCP environment with "
            "a configured service account."
        )
        raise # Re-raise the specific error for the caller to handle
    except Exception as e:
        logger.error(f"An unexpected error occurred during credential retrieval: {e}", exc_info=True)
        raise # Re-raise unexpected errors
